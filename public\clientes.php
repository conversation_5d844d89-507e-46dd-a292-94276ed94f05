<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once("../app/controllers/clientesController.php");

$clientes = getClientes($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Clientes - ERP Itapira</title>
</head>
<body>
    <h1>Clientes</h1>
    <a href="dashboard.php">Voltar</a> | 
    <a href="cliente_form.php">Novo Cliente</a>
    <hr>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr>
            <th>ID</th>
            <th>Nome</th>
            <th>Telefone</th>
            <th>Email</th>
            <th>Endereço</th>
            <th>Ações</th>
        </tr>
        <?php foreach ($clientes as $c): ?>
        <tr>
            <td><?= $c->id ?></td>
            <td><?= $c->nome ?></td>
            <td><?= $c->telefone ?></td>
            <td><?= $c->email ?></td>
            <td><?= $c->endereco ?></td>
            <td>
                <a href="cliente_form.php?id=<?= $c->id ?>">Editar</a> | 
                <a href="cliente_delete.php?id=<?= $c->id ?>" onclick="return confirm('Excluir cliente?')">Excluir</a>
            </td>
        </tr>
        <?php endforeach; ?>
    </table>
</body>
</html>
